// 危废派车订单相关服务
import { http } from '@/utils/http.js'

// 解析字典选项（从 list 字段解析）
const parseDictOptions = (listStr) => {
  if (!listStr) return []

  return listStr.split(',').map(item => {
    const [value, label] = item.split(':')
    return { value: value.trim(), label: label.trim() }
  })
}

// 从 schema 中提取字段选项
const extractFieldOptionsFromSchema = (schema, fieldName) => {
  if (!schema || !schema.attributes) return []

  const field = schema.attributes.find(attr => attr.name === fieldName)
  if (!field || !field.list) return []

  return parseDictOptions(field.list)
}

// 缓存 schema 数据，避免重复请求
let cachedSchema = null

// 清除缓存的 schema
export const clearSchemaCache = () => {
  cachedSchema = null
  console.log('已清除 schema 缓存')
}

// 获取 schema 数据
const getSchema = async () => {
  if (cachedSchema) {
    return cachedSchema
  }

  try {
    const response = await http.get('/hwit-outer-transfer-all', {
      _page: 1,
      _time: Date.now(),
      _: Date.now()
    })

    console.log('获取 schema - API响应:', response)

    if (response && response.schema) {
      cachedSchema = response.schema
      return cachedSchema
    }

    console.warn('API响应中没有 schema 信息')
    return null
  } catch (error) {
    console.error('获取 schema 失败:', error)
    return null
  }
}

// 获取筛选选项（从接口 schema 中动态获取）
export const getFilterOptions = async () => {
  try {
    const schema = await getSchema()

    if (!schema) {
      console.warn('无法获取 schema 信息')
      return {
        applyTypeOptions: [],
        disposalTypeOptions: []
      }
    }

    // 从 schema 中提取申请类型和处置去向选项
    const applyTypeOptions = extractFieldOptionsFromSchema(schema, 'apply_type')
    const disposalTypeOptions = extractFieldOptionsFromSchema(schema, 'is_sales')

    console.log('从 schema 提取的选项:', {
      applyTypeOptions,
      disposalTypeOptions
    })

    return {
      applyTypeOptions,
      disposalTypeOptions
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error)
    // 不再返回默认选项，让调用方处理空选项
    return {
      applyTypeOptions: [],
      disposalTypeOptions: []
    }
  }
}

// 从 schema 中获取公司字典的 dictViewId
const getCompanyDictViewId = (schema) => {
  if (!schema || !schema.attributes) {
    console.warn('schema 或 schema.attributes 不存在')
    return null
  }

  console.log('schema.attributes 数量:', schema.attributes.length)

  // 查找 company_id 字段
  const companyField = schema.attributes.find(attr =>
    attr.name === 'company_id' && attr.alias === '所属公司'
  )

  if (!companyField) {
    console.warn('未找到 company_id 字段，尝试查找所有相关字段:')
    const companyRelatedFields = schema.attributes.filter(attr =>
      attr.name.includes('company') || attr.alias.includes('公司')
    )
    console.log('公司相关字段:', companyRelatedFields.map(f => ({ name: f.name, alias: f.alias })))
    return null
  }

  console.log('找到公司字段:', { name: companyField.name, alias: companyField.alias })

  if (!companyField.typeObject) {
    console.warn('公司字段没有 typeObject')
    return null
  }

  if (!companyField.typeObject.id) {
    console.warn('公司字段的 typeObject 没有 id')
    return null
  }

  console.log('成功获取 dictViewId:', companyField.typeObject.id)
  return companyField.typeObject.id
}

// 获取所属公司字典数据
export const getCompanyList = async () => {
  try {
    console.log('开始获取公司列表...')

    // 获取 schema 数据
    const schema = await getSchema()

    if (!schema) {
      console.warn('无法获取 schema 信息')
      return []
    }

    // 从 schema 中动态获取公司字典的 dictViewId
    const dictViewId = getCompanyDictViewId(schema)

    if (!dictViewId) {
      console.warn('无法从 schema 中获取公司字典的 dictViewId')
      return []
    }

    console.log('从 schema 中获取的公司字典 dictViewId:', dictViewId)

    // 使用动态获取的 dictViewId 请求公司列表
    const result = await requestCompanyData(dictViewId)

    console.log('获取公司列表结果，数量:', result.length)
    return result

  } catch (error) {
    console.error('获取公司列表失败:', error)
    return []
  }
}

// 请求公司数据的通用方法（带重试机制）
const requestCompanyData = async (dictViewId, retryCount = 0) => {
  const maxRetries = 2

  try {
    const timestamp = Date.now()
    const apiParams = {
      dictViewId: dictViewId,
      _: timestamp
    }

    console.log(`获取公司列表API请求参数 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, apiParams)

    const response = await http.get('/getDictData', apiParams)
    console.log('获取公司列表API响应类型:', typeof response, '是否为数组:', Array.isArray(response))

    if (response) {
      console.log('API响应结构:', Object.keys(response))

      // 检查是否是认证错误
      if (response.code === 401 || response.message?.includes('认证') || response.message?.includes('登录')) {
        console.warn('检测到认证错误，响应:', response)
        if (retryCount < maxRetries) {
          console.log('等待 1 秒后重试...')
          await new Promise(resolve => setTimeout(resolve, 1000))
          return requestCompanyData(dictViewId, retryCount + 1)
        }
      }
    }

    // 返回公司列表数据
    if (Array.isArray(response)) {
      console.log('响应是数组，长度:', response.length)
      if (response.length === 0) {
        console.warn('API 返回空数组')
        if (retryCount < maxRetries) {
          console.log('等待 1 秒后重试...')
          await new Promise(resolve => setTimeout(resolve, 1000))
          return requestCompanyData(dictViewId, retryCount + 1)
        }
      }
      return response.map(item => ({
        org_id: item.org_id,
        org_name: item.org_name
      }))
    }

    // 如果返回的是对象格式，尝试从 data 或其他字段获取
    if (response && response.data && Array.isArray(response.data)) {
      console.log('从 response.data 获取数据，长度:', response.data.length)
      if (response.data.length === 0) {
        console.warn('response.data 为空数组')
        if (retryCount < maxRetries) {
          console.log('等待 1 秒后重试...')
          await new Promise(resolve => setTimeout(resolve, 1000))
          return requestCompanyData(dictViewId, retryCount + 1)
        }
      }
      return response.data.map(item => ({
        org_id: item.org_id,
        org_name: item.org_name
      }))
    }

    // 如果返回的是对象格式，尝试从 collection 字段获取
    if (response && response.collection && Array.isArray(response.collection)) {
      console.log('从 response.collection 获取数据，长度:', response.collection.length)
      if (response.collection.length === 0) {
        console.warn('response.collection 为空数组')
        if (retryCount < maxRetries) {
          console.log('等待 1 秒后重试...')
          await new Promise(resolve => setTimeout(resolve, 1000))
          return requestCompanyData(dictViewId, retryCount + 1)
        }
      }
      return response.collection.map(item => ({
        org_id: item.org_id,
        org_name: item.org_name
      }))
    }

    console.warn('无法从响应中提取公司数据，响应内容:', response)
    if (retryCount < maxRetries) {
      console.log('等待 1 秒后重试...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      return requestCompanyData(dictViewId, retryCount + 1)
    }

    return []
  } catch (error) {
    console.error(`请求公司数据失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error)

    if (retryCount < maxRetries) {
      console.log('等待 1 秒后重试...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      return requestCompanyData(dictViewId, retryCount + 1)
    }

    throw error
  }
}



// 获取危废派车列表 - 真实API（带重试机制）
export const getOrderList = async (params = {}, retryCount = 0) => {
  try {
    const {
      page = 1,
      car_id = '',   // 车辆ID筛选
      status,        // 状态筛选
      keyword,       // 关键词搜索（固废名称）
      applyType,     // 申请类型：equ装置, station固废站
      disposalType,  // 处置去向：0直接外委处置, 1有价值处置, 2环保科技处置, 3无价值处置
      company        // 所属公司
    } = params

    // 构造API请求参数，参考实际API调用方式
    const timestamp = Date.now()
    const apiParams = {
      _page: page,
      _time: timestamp,
      _: timestamp
    }

    // 只有当参数有值且不为空时才添加到请求中
    if (car_id) {
      apiParams.car_id = car_id
    }

    if (status && status !== 'all') {
      apiParams.bpm_status = status
    }

    if (company) {
      apiParams.company_id = company
    }

    if (applyType) {
      apiParams.apply_type = applyType
    }

    if (disposalType) {
      apiParams.is_sales = disposalType
    }

    if (keyword) {
      apiParams.waste_name = keyword
    }

    // parent_org_id 暂时不使用，如果需要可以添加



    console.log('危废派车列表API请求参数:', apiParams)

    const response = await http.get('/hwit-outer-transfer-all', apiParams)
    console.log('危废派车列表API响应:', response)

    // 直接返回API响应数据，不进行复杂的映射
    if (response && response.data && response.data.collection) {
      return {
        code: 200,
        status: 200,
        data: response.data.collection, // 直接使用原始数据
        total: response.data.count,
        pagination: response.data.pagination,
        actions: response.actions,
        schema: response.schema // 保留 schema 信息
      }
    }

    return response

  } catch (error) {
    console.error('获取危废派车列表失败:', error)

    // 如果是认证错误且建议重试，则自动重试一次
    if (error.shouldRetry && retryCount === 0) {
      console.log('检测到认证错误，自动重试请求')
      await new Promise(resolve => setTimeout(resolve, 500)) // 等待500ms
      return getOrderList(params, retryCount + 1)
    }

    // 如果API失败，抛出错误
    throw error
  }
}



// 获取订单详情
export const getOrderDetail = async (orderId) => {
  try {
    const response = await http.get(`/hwit-outer-transfer-division/${orderId}`)
    if (response && response.data) {
      return {
        code: 200,
        data: mapApiOrderToFrontend(response.data)
      }
    }
    return response
  } catch (error) {
    console.error('获取订单详情失败:', error)
    throw error
  }
}

// 导出默认对象
export default {
  getOrderList,
  getOrderDetail
}